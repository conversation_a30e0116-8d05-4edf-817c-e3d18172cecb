import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

void main() {
  runApp(const NissoTVApp());
}

class NissoTVApp extends StatelessWidget {
  const NissoTVApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Nisso TV App',
      theme: ThemeData(
        // Dark theme optimized for TV viewing
        brightness: Brightness.dark,
        colorScheme: ColorScheme.fromSeed(
          seedColor: Colors.blue,
          brightness: Brightness.dark,
        ),
        // Large text for 10-foot UI
        textTheme: const TextTheme(
          headlineLarge: TextStyle(fontSize: 48, fontWeight: FontWeight.bold),
          headlineMedium: TextStyle(fontSize: 36, fontWeight: FontWeight.w600),
          bodyLarge: TextStyle(fontSize: 24),
          bodyMedium: TextStyle(fontSize: 20),
        ),
        // Focus styling for TV navigation
        focusTheme: FocusThemeData(
          glowFactor: 2.0,
          glowColor: Colors.blue.withOpacity(0.8),
        ),
      ),
      home: const TVHomePage(),
    );
  }
}

class TVHomePage extends StatefulWidget {
  const TVHomePage({super.key});

  @override
  State<TVHomePage> createState() => _TVHomePageState();
}

class _TVHomePageState extends State<TVHomePage> {
  int _selectedIndex = 0;
  final List<String> _menuItems = [
    'Home',
    'Movies',
    'TV Shows',
    'Sports',
    'Settings',
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: RawKeyboardListener(
        focusNode: FocusNode(),
        autofocus: true,
        onKey: _handleKeyEvent,
        child: SafeArea(
          child: Column(
            children: [
              // Header with app title
              Container(
                padding: const EdgeInsets.all(32.0),
                child: Row(
                  children: [
                    Icon(
                      Icons.tv,
                      size: 48,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                    const SizedBox(width: 16),
                    Text(
                      'Nisso TV',
                      style: Theme.of(context).textTheme.headlineLarge,
                    ),
                  ],
                ),
              ),

              // Main navigation menu
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 32.0),
                  child: GridView.builder(
                    gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: 3,
                      crossAxisSpacing: 24,
                      mainAxisSpacing: 24,
                      childAspectRatio: 2.5,
                    ),
                    itemCount: _menuItems.length,
                    itemBuilder: (context, index) {
                      return TVMenuCard(
                        title: _menuItems[index],
                        icon: _getIconForMenuItem(_menuItems[index]),
                        isSelected: index == _selectedIndex,
                        onTap: () {
                          setState(() {
                            _selectedIndex = index;
                          });
                          _handleMenuSelection(_menuItems[index]);
                        },
                      );
                    },
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _handleKeyEvent(RawKeyEvent event) {
    if (event is RawKeyDownEvent) {
      final key = event.logicalKey;

      if (key == LogicalKeyboardKey.arrowLeft) {
        _navigateLeft();
      } else if (key == LogicalKeyboardKey.arrowRight) {
        _navigateRight();
      } else if (key == LogicalKeyboardKey.arrowUp) {
        _navigateUp();
      } else if (key == LogicalKeyboardKey.arrowDown) {
        _navigateDown();
      } else if (key == LogicalKeyboardKey.select ||
                 key == LogicalKeyboardKey.enter ||
                 key == LogicalKeyboardKey.space) {
        _selectCurrentItem();
      }
    }
  }

  void _navigateLeft() {
    setState(() {
      if (_selectedIndex % 3 > 0) {
        _selectedIndex--;
      }
    });
  }

  void _navigateRight() {
    setState(() {
      if (_selectedIndex % 3 < 2 && _selectedIndex < _menuItems.length - 1) {
        _selectedIndex++;
      }
    });
  }

  void _navigateUp() {
    setState(() {
      if (_selectedIndex >= 3) {
        _selectedIndex -= 3;
      }
    });
  }

  void _navigateDown() {
    setState(() {
      if (_selectedIndex + 3 < _menuItems.length) {
        _selectedIndex += 3;
      }
    });
  }

  void _selectCurrentItem() {
    _handleMenuSelection(_menuItems[_selectedIndex]);
  }

  IconData _getIconForMenuItem(String item) {
    switch (item) {
      case 'Home':
        return Icons.home;
      case 'Movies':
        return Icons.movie;
      case 'TV Shows':
        return Icons.tv;
      case 'Sports':
        return Icons.sports_soccer;
      case 'Settings':
        return Icons.settings;
      default:
        return Icons.apps;
    }
  }

  void _handleMenuSelection(String item) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Selected: $item'),
        duration: const Duration(seconds: 2),
      ),
    );
  }
}

class TVMenuCard extends StatefulWidget {
  final String title;
  final IconData icon;
  final bool isSelected;
  final VoidCallback onTap;

  const TVMenuCard({
    super.key,
    required this.title,
    required this.icon,
    required this.isSelected,
    required this.onTap,
  });

  @override
  State<TVMenuCard> createState() => _TVMenuCardState();
}

class _TVMenuCardState extends State<TVMenuCard> {
  bool _isFocused = false;

  @override
  Widget build(BuildContext context) {
    return Focus(
      onFocusChange: (hasFocus) {
        setState(() {
          _isFocused = hasFocus;
        });
      },
      onKeyEvent: (node, event) {
        if (event is KeyDownEvent &&
            event.logicalKey == LogicalKeyboardKey.select) {
          widget.onTap();
          return KeyEventResult.handled;
        }
        return KeyEventResult.ignored;
      },
      child: GestureDetector(
        onTap: widget.onTap,
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 200),
          decoration: BoxDecoration(
            color: _isFocused
                ? Theme.of(context).colorScheme.primary.withOpacity(0.8)
                : Theme.of(context).colorScheme.surface,
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: _isFocused
                  ? Theme.of(context).colorScheme.primary
                  : Colors.transparent,
              width: 3,
            ),
            boxShadow: _isFocused
                ? [
                    BoxShadow(
                      color: Theme.of(context).colorScheme.primary.withOpacity(0.5),
                      blurRadius: 20,
                      spreadRadius: 2,
                    ),
                  ]
                : null,
          ),
          child: Padding(
            padding: const EdgeInsets.all(24.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  widget.icon,
                  size: 48,
                  color: _isFocused
                      ? Colors.white
                      : Theme.of(context).colorScheme.onSurface,
                ),
                const SizedBox(height: 12),
                Text(
                  widget.title,
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: _isFocused
                        ? Colors.white
                        : Theme.of(context).colorScheme.onSurface,
                    fontWeight: _isFocused ? FontWeight.bold : FontWeight.normal,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
